//
//  NavigationDestination+Views.swift
//  CStory
//
//  Created by NZUE on 2025/7/12.
//

import SwiftData
import SwiftUI

// MARK: - View Generation

extension NavigationDestination {
  
  /// 返回每个导航目的地对应的视图
  /// 
  /// 根据导航目的地类型，返回相应的SwiftUI视图。
  /// 所有视图都通过Wrapper进行依赖注入，确保数据管理的一致性。
  ///
  /// - Parameter modelContext: SwiftData 模型上下文
  /// - Returns: 对应的 SwiftUI 视图
  ///
  /// ## 使用示例
  /// ```swift
  /// NavigationStack(path: $pathManager.path) {
  ///   HomeView()
  ///     .navigationDestination(for: NavigationDestination.self) { destination in
  ///       destination.destinationView(modelContext: modelContext)
  ///     }
  /// }
  /// ```
  @ViewBuilder
  func destinationView(modelContext: ModelContext) -> some View {
    switch self {
    // MARK: - 卡片管理
    case .cardCategoryView:
      CardCategoryViewWrapper()
    case .cardBagView:
      CardBagViewWrapper()
    case .selectBankView(let isCredit, let mainCategory):
      SelectBankViewWrapper(isCredit: isCredit, mainCategory: mainCategory)
      
    // MARK: - 交易相关
    case .transactionRecordView:
      TransactionRecordViewWrapper()
    case .transactionDetailView(let transactionId):
      TransactionDetailViewWrapper(transactionId: transactionId)
    case .transactionRefundView(let transaction):
      TransactionRefundViewWrapper(transaction: transaction)
      
    // MARK: - 分类管理
    case .t_CategoryView:
      TransactionCategoryViewWrapper()
    case .createTransactionCategory(let isMainCategory, let mainCategoryId, let selectedType):
      CategoryFormViewWrapper(
        mode: .create(
          isMainCategory: isMainCategory, 
          mainCategoryId: mainCategoryId, 
          selectedType: selectedType
        )
      )
    case .editTransactionCategoryView(let categoryId, let isMainCategory):
      CategoryFormViewWrapper(
        mode: .edit(categoryId: categoryId, isMainCategory: isMainCategory)
      )
    case .categorySort(let mode):
      CategorySortViewWrapper(mode: mode)
      
    // MARK: - 其他功能
    case .currencyRateView:
      CurrencyRateViewWrapper()
    case .transactionSettingsView:
      TransactionSettingsViewWrapper()
    case .dataResetView:
      DataResetViewWrapper()
    case .iCloudSyncView:
      iCloudSyncViewWrapper()
    }
  }
}
