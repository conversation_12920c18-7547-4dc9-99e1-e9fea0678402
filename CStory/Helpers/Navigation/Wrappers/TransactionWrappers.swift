//
//  TransactionWrappers.swift
//  CStory
//
//  Created by NZUE on 2025/7/12.
//

import SwiftData
import SwiftUI

// MARK: - Transaction View Wrappers

/// 交易相关视图的包装器集合
/// 
/// 提供交易模块所有视图的依赖注入包装器，确保数据管理的一致性。
/// 所有包装器都从Environment获取DataManagement实例。

// MARK: - Transaction Detail

/// TransactionDetailView 的包装器，用于从 Environment 获取 DataManagement
struct TransactionDetailViewWrapper: View {
  let transactionId: UUID
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    // 根据transactionId找到对应的TransactionModel
    if let transaction = dataManager.allTransactions.first(where: { $0.id == transactionId }) {
      TransactionDetailView(transaction: transaction, dataManager: dataManager)
    } else {
      // 如果找不到交易，显示错误页面
      TransactionNotFoundView()
    }
  }
}

/// 交易不存在时的错误视图
private struct TransactionNotFoundView: View {
  var body: some View {
    VStack(spacing: 20) {
      Image(systemName: "exclamationmark.triangle")
        .font(.system(size: 48))
        .foregroundColor(.orange)

      Text("交易不存在")
        .font(.title2)
        .fontWeight(.bold)

      Text("无法找到指定的交易记录")
        .font(.body)
        .foregroundColor(.secondary)
    }
    .padding()
    .navigationTitle("交易详情")
    .navigationBarTitleDisplayMode(.inline)
  }
}

// MARK: - Transaction Record

/// TransactionRecordView 的包装器，用于从 Environment 获取 DataManagement
struct TransactionRecordViewWrapper: View {
  @Environment(\.dataManager) private var dataManager
  @EnvironmentObject private var pathManager: PathManagerHelper

  var body: some View {
    TransactionRecordView(
      viewModel: TransactionRecordVM(
        dataManager: dataManager,
        onTransactionTap: { transaction in
          // 震动反馈 - 统一使用.selection强度
          dataManager.hapticManager.trigger(.selection)
          // 导航到交易详情页面
          pathManager.path.append(NavigationDestination.transactionDetailView(transaction.id))
        }
      )
    )
  }
}

// MARK: - Transaction Refund

/// TransactionRefundView 的包装器，用于从 Environment 获取 DataManagement
struct TransactionRefundViewWrapper: View {
  let transaction: TransactionModel
  @Environment(\.dataManager) private var dataManager

  var body: some View {
    TransactionRefundView(transaction: transaction, dataManager: dataManager)
  }
}

// MARK: - Manual Transaction

/// ManualTransactionView 的包装器，用于从 Environment 获取依赖并构造 VM
struct ManualTransactionViewWrapper: View {
  @Environment(\.dataManager) private var dataManager
  @Binding var showTopContent: Bool
  @Binding var showBottomContent: Bool
  var onTransactionSaved: (() -> Void)? = nil
  var onCloseRequested: (() -> Void)? = nil

  init(
    showTopContent: Binding<Bool>,
    showBottomContent: Binding<Bool>,
    onTransactionSaved: (() -> Void)? = nil,
    onCloseRequested: (() -> Void)? = nil
  ) {
    self._showTopContent = showTopContent
    self._showBottomContent = showBottomContent
    self.onTransactionSaved = onTransactionSaved
    self.onCloseRequested = onCloseRequested
  }

  var body: some View {
    ManualTransactionView(
      viewModel: ManualTransactionVM(dataManager: dataManager),
      showTopContent: $showTopContent,
      showBottomContent: $showBottomContent,
      onTransactionSaved: onTransactionSaved,
      onCloseRequested: onCloseRequested
    )
  }
}

// MARK: - AI Transaction

/// AITransactionView 的包装器，用于从 Environment 获取依赖（后续 VM 也将改为构造注入）
struct AITransactionViewWrapper: View {
  @Environment(\.dataManager) private var dataManager
  @Environment(\.modelContext) private var modelContext
  @Binding var showTopContent: Bool
  @Binding var showBottomContent: Bool
  var onSwitchToManual: (() -> Void)?
  var onCloseRequested: (() -> Void)?

  init(
    showTopContent: Binding<Bool>,
    showBottomContent: Binding<Bool>,
    onSwitchToManual: (() -> Void)? = nil,
    onCloseRequested: (() -> Void)? = nil
  ) {
    self._showTopContent = showTopContent
    self._showBottomContent = showBottomContent
    self.onSwitchToManual = onSwitchToManual
    self.onCloseRequested = onCloseRequested
  }

  var body: some View {
    AITransactionView(
      viewModel: AITransactionViewModel(dataManager: dataManager, modelContext: modelContext),
      showTopContent: $showTopContent,
      showBottomContent: $showBottomContent,
      onSwitchToManual: onSwitchToManual,
      onCloseRequested: onCloseRequested
    )
  }
}
